package br.com.pacto.swagger;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Controller para garantir que os endpoints do Swagger funcionem corretamente
 * com SpringFox 2.6.1 e Spring 4.0.3.
 * 
 * Este controller resolve problemas de mapeamento de recursos estáticos
 * do Swagger quando usado com versões específicas do Spring Framework.
 */
@Controller
public class SwaggerController {

    /**
     * Redireciona para o Swagger UI quando acessado diretamente.
     */
    @RequestMapping(value = "/swagger", method = RequestMethod.GET)
    public void swagger(HttpServletResponse response) throws IOException {
        response.sendRedirect("swagger-ui.html");
    }

    /**
     * Endpoint de verificação para garantir que o Swagger está funcionando.
     */
    @RequestMapping(value = "/swagger-check", method = RequestMethod.GET)
    @ResponseBody
    public String swaggerCheck() {
        return "{\"status\":\"Swagger está funcionando\",\"version\":\"2.6.1\"}";
    }

    /**
     * Endpoint alternativo para acessar a documentação da API.
     */
    @RequestMapping(value = "/api-docs", method = RequestMethod.GET)
    public void apiDocs(HttpServletResponse response) throws IOException {
        response.sendRedirect("v2/api-docs");
    }
}
