package br.com.pacto.swagger;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * Configuração adicional para garantir que o Swagger funcione corretamente
 * com SpringFox 2.6.1 e Spring 4.0.3.
 * 
 * Esta configuração resolve problemas específicos de mapeamento de recursos
 * e endpoints do Swagger em versões mais antigas do Spring Framework.
 */
@Configuration
public class SwaggerWebConfig extends WebMvcConfigurerAdapter {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Configuração para recursos estáticos do Swagger UI
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");

        // Configuração específica para SpringFox 2.6.1
        registry.addResourceHandler("/swagger-resources/**")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/v2/api-docs")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/configuration/ui")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/configuration/security")
                .addResourceLocations("classpath:/META-INF/resources/");
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // Redireciona a raiz do swagger para o swagger-ui.html
        registry.addRedirectViewController("/swagger", "/swagger-ui.html");
        registry.addRedirectViewController("/swagger/", "/swagger-ui.html");
    }

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }
}
